package com.drna.shadcn.compose.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.ImageLoader
import coil3.compose.AsyncImagePainter
import coil3.compose.rememberAsyncImagePainter
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.svg.SvgDecoder
import com.drna.shadcn.compose.themes.shadcnColors

/**
 * Displays a user's profile picture or a fallback if the image is not available or fails to load.
 *
 * @param modifier Optional [Modifier] for this Avatar.
 * @param size The target size of the Avatar. Defaults to 40.dp.
 * @param imageUrl The URL of the image to display. If null or empty, behavior will depend
 *   on [fallbackText], [loadingContent], and [errorContent].
 * @param contentDescription A textual description of the avatar image for accessibility.
 *   It's recommended to provide this if [imageUrl] is used.
 * @param fallbackText Text to display if the [imageUrl] is not provided or fails to load,
 *   and no specific [errorContent] is provided. This text is often
 *   initials or a placeholder character.
 * @param loadingContent An optional composable lambda to display while the image from [imageUrl]
 *   is loading. If null, a default loading indicator (or nothing) might be shown,
 *   or it might proceed directly to [fallbackText] or [errorContent]
 *   depending on the image loading implementation.
 * @param errorContent An optional composable lambda to display if loading the image from
 *   [imageUrl] fails. If null, [fallbackText] will typically be shown.
 * @param contentScale How the image (if loaded from [imageUrl]) should be scaled inside
 *   the bounds of the Avatar. Defaults to [ContentScale.Crop].
 */
@Composable
fun Avatar(
    modifier: Modifier = Modifier,
    size: Dp = 40.dp,
    imageUrl: String? = null,
    contentDescription: String? = null,
    fallbackText: String,
    loadingContent: @Composable (() -> Unit)? = null,
    errorContent: @Composable (() -> Unit)? = null,
    contentScale: ContentScale = ContentScale.Crop
) {
    val colors = MaterialTheme.shadcnColors
    val context = LocalContext.current

    // Create an ImageLoader that supports SVG if needed (common for avatars)
    val imageLoader = remember(context) {
        ImageLoader.Builder(context)
            .components {
                add(SvgDecoder.Factory())
            }
            .build()
    }

    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(context)
            .data(imageUrl)
            .crossfade(true)
            .build(),
        imageLoader = imageLoader
    )
    val state by painter.state.collectAsStateWithLifecycle()

    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .border(1.dp, colors.border, CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // Check if the image loaded successfully or is currently loading
        when (state) {
            AsyncImagePainter.State.Empty,
            is AsyncImagePainter.State.Loading -> {
                if (loadingContent != null) {
                    loadingContent()
                } else {
                    Text(
                        text = fallbackText,
                        style = TextStyle(
                            color = colors.mutedForeground,
                            fontSize = (size.value * 0.4).sp,
                            fontWeight = FontWeight.Medium
                        )
                    )
                }
            }

            is AsyncImagePainter.State.Success -> {
                Image(
                    painter = painter,
                    contentDescription = contentDescription,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = contentScale
                )
            }

            is AsyncImagePainter.State.Error -> {
                if (errorContent != null) {
                    errorContent()
                } else {
                    Text(
                        text = fallbackText,
                        style = TextStyle(
                            color = colors.mutedForeground,
                            fontSize = (size.value * 0.4).sp,
                            fontWeight = FontWeight.Medium
                        )
                    )
                }
            }
        }
    }
}
